# HTTP Request Body Customization in n8n Declarative Style

This guide shows you how to customize HTTP request bodies for your n8n custom nodes using the declarative style.

## Basic Routing Configuration

The key to customizing HTTP request bodies is using the `routing` property in your field definitions. Here are the main patterns:

### 1. Simple Field Mapping

For basic fields that map directly to the request body:

```typescript
{
    displayName: "First Name",
    name: "firstName",
    type: 'string',
    required: true,
    default: '',
    routing: {
        send: {
            type: 'body',
            property: 'firstName',
        },
    },
}
```

### 2. Custom Property Names

When the API expects a different property name:

```typescript
{
    displayName: "Requirement",
    name: "requirementName",
    type: "string",
    default: "",
    routing: {
        send: {
            type: 'body',
            property: 'requirement', // Different from the field name
        },
    },
}
```

### 3. Nested Object Properties

For nested objects in the request body:

```typescript
{
    displayName: "Company Name",
    name: "companyName",
    type: "string",
    default: "",
    routing: {
        send: {
            type: 'body',
            property: 'company.name', // Creates nested structure
        },
    },
}
```

### 4. Fixed Collections (Arrays)

For complex arrays like phone numbers or emails:

```typescript
{
    displayName: "Phone Numbers",
    name: "phoneNumbers",
    type: "fixedCollection",
    typeOptions: {
        multipleValues: true,
    },
    routing: {
        send: {
            type: 'body',
            property: 'phoneNumbers',
            value: '={{$parameter.phoneNumbers.phoneNumber}}', // Extract the array
        },
    },
    options: [
        {
            displayName: "Phone Number",
            name: "phoneNumber",
            values: [
                {
                    displayName: "Phone Number",
                    name: "value",
                    type: "string",
                    default: "",
                },
                {
                    displayName: "Country Code",
                    name: "code",
                    type: "string",
                    default: "IN",
                },
                // ... more fields
            ],
        },
    ],
}
```

### 5. Custom Value Transformation

When you need to transform the value before sending:

```typescript
{
    displayName: "Expected Closure Date",
    name: "expectedClosureOn",
    type: "dateTime",
    default: "",
    routing: {
        send: {
            type: 'body',
            property: 'expectedClosureOn',
            value: '={{new Date($parameter.expectedClosureOn).toISOString()}}', // Transform to ISO string
        },
    },
}
```

### 6. Conditional Fields

Fields that are only sent when certain conditions are met:

```typescript
{
    displayName: "Optional Field",
    name: "optionalField",
    type: "string",
    default: "",
    routing: {
        send: {
            type: 'body',
            property: 'optionalField',
            value: '={{$parameter.optionalField ? $parameter.optionalField : undefined}}',
        },
    },
}
```

### 7. Query Parameters

For sending data as query parameters instead of body:

```typescript
{
    displayName: "Page Size",
    name: "pageSize",
    type: "number",
    default: 10,
    routing: {
        send: {
            type: 'query',
            property: 'limit',
        },
    },
}
```

### 8. Headers

For sending data as headers:

```typescript
{
    displayName: "Custom Header",
    name: "customHeader",
    type: "string",
    default: "",
    routing: {
        send: {
            type: 'header',
            property: 'X-Custom-Header',
        },
    },
}
```

## Advanced Patterns

### Pre-send Hooks

For complex transformations, you can use pre-send hooks in the operation definition:

```typescript
{
    name: 'Create',
    value: 'create',
    action: 'Create a new lead',
    description: 'Create a new lead',
    routing: {
        request: {
            method: 'POST',
            url: '/v1/leads',
        },
        send: {
            preSend: [
                async function(this: IExecuteSingleFunctions, requestOptions: IHttpRequestOptions): Promise<IHttpRequestOptions> {
                    // Custom logic to transform the request body
                    const body = requestOptions.body as IDataObject;
                    
                    // Example: Transform phone numbers format
                    if (body.phoneNumbers) {
                        body.phoneNumbers = (body.phoneNumbers as any[]).map(phone => ({
                            number: `${phone.dialCode}${phone.value}`,
                            type: phone.type,
                            primary: phone.primary
                        }));
                    }
                    
                    return requestOptions;
                }
            ]
        }
    },
}
```

### Post-receive Hooks

For transforming response data:

```typescript
routing: {
    request: {
        method: 'POST',
        url: '/v1/leads',
    },
    output: {
        postReceive: [
            {
                type: 'rootProperty',
                properties: {
                    property: 'data'
                }
            }
        ]
    }
}
```

## Best Practices

1. **Use descriptive property names** that match your API documentation
2. **Handle optional fields** properly to avoid sending empty values
3. **Transform data types** as needed (dates, numbers, etc.)
4. **Use expressions** for dynamic values and transformations
5. **Test thoroughly** with different input combinations
6. **Document your field mappings** for future maintenance

## Testing Your Configuration

After setting up your routing, test with different scenarios:

1. Required fields only
2. All optional fields filled
3. Complex collections with multiple items
4. Edge cases (empty arrays, null values, etc.)

The declarative style in n8n automatically handles the HTTP request construction based on your routing configuration, making it much easier to maintain than imperative code.
