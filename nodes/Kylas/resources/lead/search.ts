import type { INodeProperties } from 'n8n-workflow';

const showOnlyForLeadSearch = {
    operation: ['search'],
    resource: ['lead'],
};

export const leadSearchDescription: INodeProperties[] = [
    {
        displayName: "Additional Options",
        name: "additionalOptions",
        type: "collection",
        placeholder: "Add Option",
        default: {},
        displayOptions: {
            show: showOnlyForLeadSearch,
        },
        options: [
            {
                displayName: "Limit",
                name: "limit",
                type: "number",
                typeOptions: {
                    minValue: 1,
                },
                default: 50,
                description: "Max number of results to return",
            },
            {
                displayName: "Offset",
                name: "offset",
                type: "number",
                default: 0,
                description: "Number of results to skip",
            },
            {
                displayName: "Search Query",
                name: "q",
                type: "string",
                default: "",
                description: "Search query to filter leads",
            },
        ],
    },
];
