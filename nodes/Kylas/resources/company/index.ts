import type { INodeProperties } from 'n8n-workflow';
import { companyGetManyDescription } from './getAll';

const showOnlyForCompanies = {
	resource: ['company'],
};

export const companyDescription: INodeProperties[] = [
	{
		displayName: 'Operation',
		name: 'operation',
		type: 'options',
		noDataExpression: true,
		displayOptions: {
			show: showOnlyForCompanies,
		},
		options: [
			{
				name: 'Search',
				value: 'getAll',
				action: 'Get companies',
				description: 'Get companies',
			},
		],
		default: 'getAll',
	},
	...companyGetManyDescription,
];
