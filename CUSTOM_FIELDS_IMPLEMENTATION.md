# Custom Fields Implementation for Kylas n8n Node

This document explains how the dynamic custom fields loading works for the Kylas n8n node.

## Overview

The implementation fetches custom fields from the Kylas API endpoint `GET /v1/layouts/leads/system-fields?view=create` and dynamically creates form fields based on the field types returned.

## API Response Structure

The API returns an array of field objects with this structure:

```json
[
  {
    "id": 221,
    "type": "TEXT_FIELD",
    "displayName": "Company Name",
    "name": "companyName",
    "active": true,
    "required": false,
    "important": false,
    "standard": false,
    "width": 12,
    "column": 1,
    "row": 1,
    "multiValue": false,
    "internal": false,
    "picklist": null,
    "systemRequired": false
  }
]
```

## Filtering Logic

The implementation filters fields based on:
- `active: true` - Field is active
- `standard: false` - Not a standard field (custom field)
- `internal: false` - Not an internal field

## Implementation Components

### 1. LoadOptions Method

Located in `nodes/Kylas/Kylas.node.ts`:

```typescript
async getLeadCustomFields(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {
    // Fetches custom fields from API
    // Filters by field type if specified
    // Returns options for dropdown selection
}
```

### 2. Custom Fields Definitions

Located in `nodes/Kylas/resources/lead/customFields.ts`:

The implementation creates separate field groups for different field types:
- **Custom Text Fields** - For `TEXT_FIELD` type
- **Custom Number Fields** - For `NUMBER_FIELD` type  
- **Custom Date Fields** - For `DATE_FIELD` type
- **Custom Boolean Fields** - For `BOOLEAN_FIELD` type

### 3. Field Type Mapping

Each custom field type maps to appropriate n8n field types:

| API Field Type | n8n Field Type | Description |
|---------------|----------------|-------------|
| TEXT_FIELD    | string         | Text input |
| NUMBER_FIELD  | number         | Number input |
| DATE_FIELD    | dateTime       | Date/time picker |
| BOOLEAN_FIELD | boolean        | Checkbox |
| PICKLIST      | options        | Dropdown (future enhancement) |

## How It Works

### 1. Field Loading Process

1. User selects "Lead" resource and "Create" operation
2. Custom field sections appear in the form
3. When user clicks on a field name dropdown, the `getLeadCustomFields` method is called
4. API request is made to fetch available custom fields
5. Fields are filtered and returned as dropdown options

### 2. Field Selection and Value Entry

1. User selects a custom field from the dropdown
2. A corresponding value input field appears
3. User enters the value for the custom field
4. The routing configuration maps the field to the request body

### 3. Request Body Construction

The routing configuration ensures that custom fields are properly included in the POST request:

```typescript
routing: {
    send: {
        type: 'body',
        property: '={{$parameter.customTextFields.fieldName}}',
        value: '={{$parameter.customTextFields.fieldValue}}',
    },
}
```

## Example Usage

### User Workflow

1. Select "Lead" resource
2. Select "Create" operation
3. Fill in standard fields (firstName, lastName, etc.)
4. Expand "Custom Text Fields" section
5. Select a custom field from the dropdown (e.g., "Department Code")
6. Enter a value (e.g., "SALES-001")
7. Repeat for other custom field types as needed

### Resulting Request Body

```json
{
    "firstName": "John",
    "lastName": "Doe",
    "departmentCode": "SALES-001",
    "customScore": 85,
    "lastContactDate": "2024-01-15T10:30:00.000Z",
    "isVip": true
}
```

## Extending the Implementation

### Adding New Field Types

To support additional field types:

1. Add a new field group in `customFields.ts`
2. Map the API field type to appropriate n8n field type
3. Add routing configuration for the new field type

### Supporting Picklist Fields

For picklist fields, you would need to:

1. Create another loadOptions method to fetch picklist values
2. Use `type: 'options'` with `loadOptionsMethod`
3. Handle the picklist data structure from the API

### Example Picklist Implementation

```typescript
{
    displayName: "Custom Picklist Fields",
    name: "customPicklistFields",
    type: "collection",
    options: [
        {
            displayName: "Field Name",
            name: "fieldName",
            type: "options",
            typeOptions: {
                loadOptionsMethod: "getLeadCustomPicklistFields",
            },
        },
        {
            displayName: "Field Value",
            name: "fieldValue",
            type: "options",
            typeOptions: {
                loadOptionsMethod: "getPicklistValues",
                loadOptionsDependsOn: ["fieldName"],
            },
        },
    ],
}
```

## Benefits

1. **Dynamic Field Loading** - No need to hardcode custom fields
2. **Type Safety** - Proper field types based on API metadata
3. **User-Friendly** - Clear field names and descriptions
4. **Maintainable** - Automatically adapts to API changes
5. **Extensible** - Easy to add support for new field types

## Testing

To test the custom fields implementation:

1. Ensure your Kylas API credentials are configured
2. Create a test workflow with the Kylas node
3. Select Lead > Create operation
4. Verify that custom field dropdowns populate with API data
5. Test creating a lead with custom field values
6. Verify the request body includes the custom fields correctly

## Troubleshooting

### Common Issues

1. **Empty Dropdowns** - Check API credentials and endpoint accessibility
2. **Missing Fields** - Verify field filtering logic (standard=false, internal=false)
3. **Wrong Field Types** - Check field type mapping in the implementation
4. **Request Errors** - Verify routing configuration and field name mapping
