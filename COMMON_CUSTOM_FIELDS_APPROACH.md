# Common Custom Fields Implementation

This document explains the new unified approach for handling custom fields in the Kylas n8n node, where a single common function dynamically handles all field types based on the API response.

## Overview

Instead of creating separate field collections for each type (text, number, date, etc.), we now use a single `customFields` collection that dynamically adapts the input type based on the field metadata from the API.

## Key Benefits

1. **Single Implementation** - One field collection handles all custom field types
2. **Dynamic Type Detection** - Input type changes based on API field type
3. **Automatic Adaptation** - New field types from API are automatically supported
4. **Cleaner Code** - Less repetitive field definitions
5. **Better Maintainability** - Changes only need to be made in one place

## How It Works

### 1. Field Metadata Loading

The `getLeadCustomFieldsWithType` method fetches custom fields and returns them with embedded metadata:

```typescript
return customFields.map(field => ({
    name: `${field.displayName} (${field.type})`,
    value: JSON.stringify({
        name: field.name,
        type: field.type,
        required: field.required,
        picklist: field.picklist
    }),
    description: `Type: ${field.type}${field.required ? ' (Required)' : ''}`,
}));
```

### 2. Dynamic Input Types

Based on the field type detected in the metadata, different input fields are shown:

| API Field Type | Input Type | Field Name | Description |
|---------------|------------|------------|-------------|
| TEXT_FIELD    | string     | textValue  | Text input |
| NUMBER_FIELD  | number     | numberValue| Number input |
| DATE_FIELD    | dateTime   | dateValue  | Date picker |
| BOOLEAN_FIELD | boolean    | booleanValue| Checkbox |
| PICKLIST      | options    | picklistValue| Dropdown |

### 3. Smart Field Display

The UI uses `displayOptions` with regex patterns to show the appropriate input:

```typescript
displayOptions: {
    show: {
        fieldName: ['/NUMBER_FIELD/'], // Shows only for number fields
    }
}
```

### 4. Request Body Construction

The `preSend` function processes the field metadata and constructs the request body:

```typescript
// Parse field metadata
const fieldMeta = JSON.parse(field.fieldName);
const fieldName = fieldMeta.name;
const fieldType = fieldMeta.type;

// Select appropriate value based on type
switch (fieldType) {
    case 'TEXT_FIELD':
        value = field.textValue;
        break;
    case 'NUMBER_FIELD':
        value = field.numberValue;
        break;
    // ... etc
}

// Add to request body
requestOptions.body[fieldName] = value;
```

## Implementation Details

### Field Type Mapping

The system supports these field types out of the box:

```typescript
const fieldTypeMapping = {
    'TEXT_FIELD': { n8nType: 'string', defaultValue: '' },
    'NUMBER_FIELD': { n8nType: 'number', defaultValue: 0 },
    'DATE_FIELD': { n8nType: 'dateTime', defaultValue: '' },
    'BOOLEAN_FIELD': { n8nType: 'boolean', defaultValue: false },
    'PICKLIST': { n8nType: 'options', defaultValue: '' },
    'EMAIL_FIELD': { n8nType: 'string', defaultValue: '' },
    'URL_FIELD': { n8nType: 'string', defaultValue: '' },
    'TEXTAREA_FIELD': { n8nType: 'string', defaultValue: '' }
};
```

### Picklist Handling

For picklist fields, the system:
1. Extracts picklist ID from field metadata
2. Fetches available options from `/v1/picklists/{id}/values`
3. Populates dropdown with active values

### Error Handling

The implementation includes robust error handling:
- JSON parsing errors fall back to simple field name treatment
- Missing picklist data returns empty options
- Invalid field types default to text input

## User Experience

### Field Selection Process

1. User clicks "Add Custom Field"
2. Dropdown shows: "Company Size (NUMBER_FIELD)", "Department (TEXT_FIELD)", etc.
3. User selects a field
4. Appropriate input type appears automatically
5. User enters value in the correct format

### Example Workflow

```
1. Select "Company Size (NUMBER_FIELD)"
   → Number input appears
   
2. Select "Is VIP (BOOLEAN_FIELD)"
   → Checkbox appears
   
3. Select "Lead Source (PICKLIST)"
   → Dropdown with API values appears
   
4. Select "Notes (TEXT_FIELD)"
   → Text input appears
```

## Request Body Result

The final request body includes properly typed values:

```json
{
    "firstName": "John",
    "lastName": "Doe",
    "companySize": 500,           // NUMBER_FIELD
    "isVip": true,               // BOOLEAN_FIELD
    "leadSource": "WEBSITE",     // PICKLIST
    "notes": "Important client"  // TEXT_FIELD
}
```

## Extending the System

### Adding New Field Types

To support a new field type from the API:

1. Add the type to `fieldTypeMapping` (optional, for documentation)
2. Add a new input field in the `values` array
3. Add a case in the `preSend` switch statement
4. Update the `displayOptions` regex if needed

### Example: Adding CURRENCY_FIELD

```typescript
// Add input field
{
    displayName: "Value (Currency)",
    name: "currencyValue",
    type: "number",
    typeOptions: {
        numberPrecision: 2,
    },
    displayOptions: {
        show: {
            fieldName: ['/CURRENCY_FIELD/'],
        }
    }
}

// Add to preSend switch
case 'CURRENCY_FIELD':
    value = field.currencyValue;
    break;
```

## Advantages Over Previous Approach

### Before (Multiple Collections)
- 5 separate field collections
- Repetitive code for each type
- Hard to maintain and extend
- Type filtering in loadOptions method

### After (Single Collection)
- 1 unified field collection
- Dynamic type detection
- Easy to extend with new types
- Metadata-driven approach

## Testing

To test the new implementation:

1. Verify field dropdown shows types: "Field Name (TYPE)"
2. Test that correct input appears for each type
3. Verify picklist fields load options correctly
4. Check request body contains properly typed values
5. Test error handling with invalid field metadata

This approach provides a much cleaner, more maintainable solution that automatically adapts to new field types from your API!
